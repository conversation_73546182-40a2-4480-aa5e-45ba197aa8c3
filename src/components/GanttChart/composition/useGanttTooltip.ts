import { ref, reactive, onBeforeUnmount } from 'vue'
import type { GanttBar } from '../types'

export function useGanttTooltip() {
  const tooltipVisible = ref(false)
  const tooltipContent = ref('')
  const tooltipPosition = reactive({ x: 0, y: 0 })
  
  // Create tooltip element that will be appended to body (Vue 2 compatible portal)
  let tooltipElement: HTMLElement | null = null
  
  const createTooltipElement = () => {
    if (tooltipElement) return tooltipElement
    
    tooltipElement = document.createElement('div')
    tooltipElement.className = 'farm-gantt-tooltip-overlay'
    tooltipElement.style.cssText = `
      position: fixed;
      z-index: 9999;
      pointer-events: none;
      visibility: hidden;
      opacity: 0;
      transition: visibility 0.2s linear, opacity 0.2s linear;
    `
    
    const tooltipContent = document.createElement('div')
    tooltipContent.className = 'farm-gantt-tooltip-content'
    tooltipContent.style.cssText = `
      background-color: rgba(0, 0, 0, 0.9);
      color: white;
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      max-width: 200px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      font-family: 'Manrope', sans-serif;
    `
    
    tooltipElement.appendChild(tooltipContent)
    document.body.appendChild(tooltipElement)
    
    return tooltipElement
  }
  
  const showTooltip = (event: MouseEvent, bar: GanttBar) => {
    const element = createTooltipElement()
    const contentElement = element.querySelector('.farm-gantt-tooltip-content') as HTMLElement
    
    if (contentElement) {
      contentElement.textContent = `${bar.label} - Placeholder tooltip content`
    }
    
    updatePosition(event)
    tooltipVisible.value = true
    
    // Show tooltip
    element.style.visibility = 'visible'
    element.style.opacity = '1'
  }
  
  const hideTooltip = () => {
    if (tooltipElement) {
      tooltipElement.style.visibility = 'hidden'
      tooltipElement.style.opacity = '0'
    }
    tooltipVisible.value = false
  }
  
  const updatePosition = (event: MouseEvent) => {
    const offset = 10
    tooltipPosition.x = event.clientX + offset
    tooltipPosition.y = event.clientY - 40
    
    if (tooltipElement) {
      tooltipElement.style.left = `${tooltipPosition.x}px`
      tooltipElement.style.top = `${tooltipPosition.y}px`
    }
  }
  
  // Cleanup when component is destroyed
  onBeforeUnmount(() => {
    if (tooltipElement && tooltipElement.parentNode) {
      tooltipElement.parentNode.removeChild(tooltipElement)
      tooltipElement = null
    }
  })
  
  return {
    tooltipVisible,
    tooltipContent,
    tooltipPosition,
    showTooltip,
    hideTooltip,
    updatePosition
  }
}